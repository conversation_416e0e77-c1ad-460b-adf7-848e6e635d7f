{"name": "ui5_sap_fidoc", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tanstack/react-query": "^5.66.9", "@ui5/webcomponents": "^2.7.3", "@ui5/webcomponents-fiori": "^2.7.3", "@ui5/webcomponents-icons": "^2.7.3", "@ui5/webcomponents-react": "^2.7.2", "axios": "^1.7.9", "js-cookie": "^3.0.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router": "^7.2.0"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "vite": "^6.2.0"}}