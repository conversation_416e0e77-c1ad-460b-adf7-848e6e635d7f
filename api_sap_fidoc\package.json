{"name": "api_sap_fidoc", "version": "1.0.0", "description": "SAP Finential Document Posting", "type": "module", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js"}, "repository": {"type": "git", "url": "git+https://gitlab.com/matw_integration/SAP_FI_Document_Maintain.git"}, "keywords": [], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "ISC", "bugs": {"url": "https://gitlab.com/matw_integration/SAP_FI_Document_Maintain/issues"}, "homepage": "https://gitlab.com/matw_integration/SAP_FI_Document_Maintain#readme", "dependencies": {"@joi/date": "^2.1.1", "axios": "^1.7.2", "basic-auth": "^2.0.1", "cookie-parser": "^1.4.6", "express": "^4.19.2", "express-jwt": "^8.4.1", "express-session": "^1.18.0", "helmet": "^5.1.1", "http-errors": "^2.0.0", "joi": "^17.13.3", "jwks-rsa": "^3.1.0", "kafkajs": "^2.2.4", "mongoose": "^6.13.0", "morgan": "^1.10.0", "node-rfc": "^2.7.1"}, "devDependencies": {"dotenv": "^16.4.5", "nodemon": "^3.1.4"}}