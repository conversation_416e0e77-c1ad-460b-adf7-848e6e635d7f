Playing with the sample programs
================================

The sample programs in this folder are intended for giving you a leg up with the most 
common tasks encountered when writing your first RFC program: writing a simple client,
writing a simple server, dealing with tables, using logon parameters and SSO, etc.
Just take the samples as a starting point and modify them to meet your needs.


Once you get started, you will need more detailed instructions for various topics.
Please refer to the following further sources of information:

- All logon parameters used for opening client connections as well as registering servers
  are documented in the sapnwrfc.ini file located in the same directory as this readme.

- For instructions on how to compile & link your RFC programs on various platforms and
  operating systems, please see SAP Note 2573953.

- A detailed API documentation in Doxygen format as well as a programming guide in PDF
  format can be downloaded from https://support.sap.com/nwrfcsdk

- For an insight explanation of how the SAP NW RFC Library works, the following series
  of SDN articles is recommended:

  Part I -- RFC Client Programming
  https://wiki.scn.sap.com/wiki/x/zz27Gg

  Part II -- RFC Server Programming
  https://wiki.scn.sap.com/wiki/x/9z27Gg

  Part III -- Advanced Topics
  https://wiki.scn.sap.com/wiki/x/FD67Gg
