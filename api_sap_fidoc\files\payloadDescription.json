{"ReProcessText": "Reprocessing Text", "Header": {"OBJ_TYPE": "Reference Transaction", "OBJ_KEY": "Reference Key", "OBJ_SYS": "Logical system of source document", "BUS_ACT": "Business Transaction", "USERNAME": "User name", "HEADER_TXT": "Document Header Text", "COMP_CODE": "Company Code", "DOC_DATE": "Document Date in Document", "PSTNG_DATE": "Posting Date in the Document", "TRANS_DATE": "Translation Date", "FISC_YEAR": "Fiscal Year", "FIS_PERIOD": "Fiscal Period", "DOC_TYPE": "Document Type", "REF_DOC_NO": "Reference Document Number", "AC_DOC_NO": "Accounting Document Number", "OBJ_KEY_R": "Cancel: object key (AWREF_REV and AWORG_REV)", "REASON_REV": "Reason for reversal", "COMPO_ACC": "Component in ACC Interface", "REF_DOC_NO_LONG": "Reference Document Number (for Dependencies see Long Text)", "ACC_PRINCIPLE": "Accounting Principle", "NEG_POSTNG": "Indicator: Negative posting", "OBJ_KEY_INV": "Invoice Ref.: Object Key (AWREF_REB and AWORG_REB)", "BILL_CATEGORY": "Billing category", "VATDATE": "Tax Reporting Date", "INVOICE_REC_DATE": "Invoice Receipt Date", "ECS_ENV": "ECS Environment", "PARTIAL_REV": "Indicator: <PERSON><PERSON> Reversal", "DOC_STATUS": "Document Status"}, "Items": [{"Item_Number": "Accounting Document Line Item Number", "General_Ledger": {"GL_ACCOUNT": "General <PERSON><PERSON> Account", "ITEM_TEXT": "Item Text", "STAT_CON": "Indicator for statistical line items", "LOG_PROC": "Logical Transaction", "AC_DOC_NO": "Accounting Document Number", "REF_KEY_1": "Business Partner Reference Key", "REF_KEY_2": "Business Partner Reference Key", "REF_KEY_3": "Reference Key for Line Item", "ACCT_KEY": "Transaction Key", "ACCT_TYPE": "Account Type", "DOC_TYPE": "Document Type", "COMP_CODE": "Company Code", "BUS_AREA": "Business Area", "FUNC_AREA": "Functional Area", "PLANT": "Plant", "FIS_PERIOD": "Fiscal Period", "FISC_YEAR": "Fiscal Year", "PSTNG_DATE": "Posting Date in the Document", "VALUE_DATE": "Value Date", "FM_AREA": "Financial Management Area", "CUSTOMER": "Customer Number", "CSHDIS_IND": "Indicator: Line Item Not Liable to Cash Discount?", "VENDOR_NO": "Account Number of <PERSON><PERSON>or or Creditor", "ALLOC_NMBR": "Assignment Number", "TAX_CODE": "Tax on sales/purchases code", "TAXJURCODE": "Tax Jurisdiction", "EXT_OBJECT_ID": "Technical Key of External Object", "BUS_SCENARIO": "Business Scenario in Controlling for Logistical Objects", "COSTOBJECT": "Cost Object", "COSTCENTER": "Cost Center", "ACTTYPE": "Activity Type", "PROFIT_CTR": "Profit Center", "PART_PRCTR": "Partner Profit Center", "NETWORK": "Network Number for Account Assignment", "WBS_ELEMENT": "Work Breakdown Structure Element (WBS Element)", "ORDERID": "Order Number", "ORDER_ITNO": "Order Item Number", "ROUTING_NO": "Routing number of operations in the order", "ACTIVITY": "Operation/Activity Number", "COND_TYPE": "Condition type", "COND_COUNT": "Condition Counter", "COND_ST_NO": "Level Number", "FUND": "Fund", "FUNDS_CTR": "Funds Center", "CMMT_ITEM": "Commitment Item", "CO_BUSPROC": "Business Process", "ASSET_NO": "Main Asset Number", "SUB_NUMBER": "Asset Subnumber", "BILL_TYPE": "Billing Type", "SALES_ORD": "Sales Order Number", "S_ORD_ITEM": "Item Number in Sales Order", "DISTR_CHAN": "Distribution Channel", "DIVISION": "Division", "SALESORG": "Sales Organization", "SALES_GRP": "Sales Group", "SALES_OFF": "Sales Office", "SOLD_TO": "Sold-to party", "DE_CRE_IND": "Indicator: subsequent debit/credit", "P_EL_PRCTR": "Partner profit center for elimination of internal business", "XMFRW": "Indicator: Update quantity in RW", "QUANTITY": "Quantity", "BASE_UOM": "Base Unit of Measure", "BASE_UOM_ISO": "Base unit of measure in ISO code", "INV_QTY": "Actual Invoiced Quantity", "INV_QTY_SU": "Billing quantity in stockkeeping unit", "SALES_UNIT": "Sales unit", "SALES_UNIT_ISO": "Sales unit in ISO code", "PO_PR_QNT": "Quantity in order price quantity unit", "PO_PR_UOM": "Order price unit (purchasing)", "PO_PR_UOM_ISO": "Purchase order price unit in ISO code", "ENTRY_QNT": "Quantity in Unit of Entry", "ENTRY_UOM": "Unit of Entry", "ENTRY_UOM_ISO": "Unit of entry in ISO code", "VOLUME": "Volume", "VOLUMEUNIT": "Volume unit", "VOLUMEUNIT_ISO": "Volume unit in ISO code", "GROSS_WT": "Gross Weight", "NET_WEIGHT": "Net weight", "UNIT_OF_WT": "Weight unit", "UNIT_OF_WT_ISO": "Unit of weight in ISO code", "ITEM_CAT": "Item category in purchasing document", "MATERIAL": "Material Number", "MATL_TYPE": "Material Type", "MVT_IND": "Movement Indicator", "REVAL_IND": "Revaluation", "ORIG_GROUP": "Origin Group as Subdivision of Cost Element", "ORIG_MAT": "Material-related origin", "SERIAL_NO": "Sequential number of account assignment", "PART_ACCT": "Partner Account Number", "TR_PART_BA": "Trading Partner's Business Area", "TRADE_ID": "Company ID of Trading Partner", "VAL_AREA": "Valuation Area", "VAL_TYPE": "Valuation Type", "ASVAL_DATE": "Reference Date", "PO_NUMBER": "Purchasing Document Number", "PO_ITEM": "Item Number of Purchasing Document", "ITM_NUMBER": "Item number of the SD document", "COND_CATEGORY": "Condition Category (Examples: Tax, Freight, Price, Cost)", "FUNC_AREA_LONG": "Functional Area", "CMMT_ITEM_LONG": "Commitment Item", "GRANT_NBR": "<PERSON>", "CS_TRANS_T": "Transaction Type", "MEASURE": "Funded Program", "SEGMENT": "Segment for Segmental Reporting", "PARTNER_SEGMENT": "Partner Segment for Segmental Reporting", "RES_DOC": "Document Number for Earmarked Funds", "RES_ITEM": "Earmarked Funds: Document Item", "BILLING_PERIOD_START_DATE": "Billing Period of Performance Start Date", "BILLING_PERIOD_END_DATE": "Billing Period of Performance End Date", "PPA_EX_IND": "PPA Exclude Indicator", "FASTPAY": "PPA Fast Pay Indicator", "PARTNER_GRANT_NBR": "Partner Grant", "BUDGET_PERIOD": "FM: Budget Period", "PARTNER_BUDGET_PERIOD": "FM: Partner Budget Period", "PARTNER_FUND": "Partner Fund", "ITEMNO_TAX": "Document item number refering to tax document."}, "Customer": {"CUSTOMER": "Customer Number", "GL_ACCOUNT": "General <PERSON><PERSON> Account", "REF_KEY_1": "Business Partner Reference Key", "REF_KEY_2": "Business Partner Reference Key", "REF_KEY_3": "Reference Key for Line Item", "COMP_CODE": "Company Code", "BUS_AREA": "Business Area", "PMNTTRMS": "Terms of Payment Key", "BLINE_DATE": "Baseline Date For Due Date Calculation", "DSCT_DAYS1": "Days for first cash discount", "DSCT_DAYS2": "Days for second cash discount", "NETTERMS": "Deadline for net conditions", "DSCT_PCT1": "Percentage for First Cash Discount", "DSCT_PCT2": "Percentage for Second Cash Discount", "PYMT_METH": "Payment method", "PMTMTHSUPL": "Payment Method Supplement", "PAYMT_REF": "Payment Reference", "DUNN_KEY": "Dunning keys", "DUNN_BLOCK": "Dunning block", "PMNT_BLOCK": "Payment block key", "VAT_REG_NO": "VAT Registration Number", "ALLOC_NMBR": "Assignment Number", "ITEM_TEXT": "Item Text", "PARTNER_BK": "Partner Bank Type", "SCBANK_IND": "State Central Bank Indicator", "BUSINESSPLACE": "Stores", "SECTIONCODE": "Section Code", "BRANCH": "Account number of the branch", "PYMT_CUR": "Currency for automatic payment", "PYMT_CUR_ISO": "ISO code currency", "PYMT_AMT": "Amount in Payment Currency", "C_CTR_AREA": "Credit control area", "BANK_ID": "Short Key for a House Bank", "SUPCOUNTRY": "Supplying Country", "SUPCOUNTRY_ISO": "Supplier country ISO code", "TAX_CODE": "Tax on sales/purchases code", "TAXJURCODE": "Tax Jurisdiction", "TAX_DATE": "Date Relevant for Determining the Tax Rate", "SP_GL_IND": "Special G/L Indicator", "PARTNER_GUID": "Com. Interface: Business Partner GUID", "ALT_PAYEE": "Alternative payee", "ALT_PAYEE_BANK": "Bank type of alternative payer", "DUNN_AREA": "Dunning Area", "CASE_GUID": "Technical Case Key (Case GUID)", "PROFIT_CTR": "Profit Center", "FUND": "Fund", "GRANT_NBR": "<PERSON>", "MEASURE": "Funded Program", "HOUSEBANKACCTID": "ID for Account Details", "RES_DOC": "Document Number for Earmarked Funds", "RES_ITEM": "Earmarked Funds: Document Item", "FUND_LONG": "Long Fund (Obsolete)", "DISPUTE_IF_TYPE": "Dispute Management: Dispute Interface Category", "BUDGET_PERIOD": "FM: Budget Period", "PAYS_PROV": "Payment Service Provider", "PAYS_TRAN": "Payment Reference of Payment Service Provider", "SEPA_MANDATE_ID": "Unique Reference to Mandate per Payment Recipient", "PART_BUSINESSPLACE": "Branch Code", "REP_COUNTRY_EU": "Reporting Country for Delivery of Goods within the EU"}, "CustomerClearing": {"BELNR": "Customer Invoice Accounting Document Number", "BUKRS": "Company Code", "GJAHR": "Fiscal Year", "BUZEI": "Number of Line Item Within Customer Invoice Accounting Document"}, "Vendor": {"VENDOR_NO": "Account Number of <PERSON><PERSON>or or Creditor", "GL_ACCOUNT": "General <PERSON><PERSON> Account", "REF_KEY_1": "Business Partner Reference Key", "REF_KEY_2": "Business Partner Reference Key", "REF_KEY_3": "Reference Key for Line Item", "COMP_CODE": "Company Code", "BUS_AREA": "Business Area", "PMNTTRMS": "Terms of Payment Key", "BLINE_DATE": "Baseline Date For Due Date Calculation", "DSCT_DAYS1": "Days for first cash discount", "DSCT_DAYS2": "Days for second cash discount", "NETTERMS": "Deadline for net conditions", "DSCT_PCT1": "Percentage for First Cash Discount", "DSCT_PCT2": "Percentage for Second Cash Discount", "PYMT_METH": "Payment method", "PMTMTHSUPL": "Payment Method Supplement", "PMNT_BLOCK": "Payment block key", "SCBANK_IND": "State Central Bank Indicator", "SUPCOUNTRY": "Supplying Country", "SUPCOUNTRY_ISO": "Supplier country ISO code", "BLLSRV_IND": "Service Indicator (Foreign Payment)", "ALLOC_NMBR": "Assignment Number", "ITEM_TEXT": "Item Text", "PO_SUB_NO": "ISR Subscriber Number", "PO_CHECKDG": "ISR Check Digit", "PO_REF_NO": "ISR Reference Number", "W_TAX_CODE": "Withholding tax code", "BUSINESSPLACE": "Stores", "SECTIONCODE": "Section Code", "INSTR1": "Instruction 1", "INSTR2": "Instruction 2", "INSTR3": "Instruction 3", "INSTR4": "Instruction 4", "BRANCH": "Account number of the branch", "PYMT_CUR": "Currency for automatic payment", "PYMT_AMT": "Amount in Payment Currency", "PYMT_CUR_ISO": "ISO code currency", "SP_GL_IND": "Special G/L Indicator", "TAX_CODE": "Tax on sales/purchases code", "TAX_DATE": "Date Relevant for Determining the Tax Rate", "TAXJURCODE": "Tax Jurisdiction", "ALT_PAYEE": "Alternative payee", "ALT_PAYEE_BANK": "Bank type of alternative payer", "PARTNER_BK": "Partner Bank Type", "BANK_ID": "Short Key for a House Bank", "PARTNER_GUID": "Com. Interface: Business Partner GUID", "PROFIT_CTR": "Profit Center", "FUND": "Fund", "GRANT_NBR": "<PERSON>", "MEASURE": "Funded Program", "HOUSEBANKACCTID": "ID for Account Details", "BUDGET_PERIOD": "FM: Budget Period", "PPA_EX_IND": "PPA Exclude Indicator", "PART_BUSINESSPLACE": "Branch Code", "PAYMT_REF": "Payment Reference"}, "Tax": {"GL_ACCOUNT": "General <PERSON><PERSON> Account", "COND_KEY": "Condition Type", "ACCT_KEY": "Transaction Key", "TAX_CODE": "Tax on sales/purchases code", "TAX_RATE": "Tax rate", "TAX_DATE": "Date Relevant for Determining the Tax Rate", "TAXJURCODE": "Tax Jurisdiction", "TAXJURCODE_DEEP": "Tax jurisdiction code - jurisdiction for lowest level tax", "TAXJURCODE_LEVEL": "Tax Jurisdiction Code Level", "ITEMNO_TAX": "Document item number refering to tax document.", "DIRECT_TAX": "Indicator: Direct Tax Posting"}, "Currency": {"CURR_TYPE": "Currency Type and Valuation View", "CURRENCY": "<PERSON><PERSON><PERSON><PERSON> Key", "CURRENCY_ISO": "ISO code currency", "AMT_DOCCUR": "Amount in Document Currency", "EXCH_RATE": "Exchange rate", "EXCH_RATE_V": "Indirect quoted exchange rate", "AMT_BASE": "Tax Base Amount in Document Currency", "DISC_BASE": "Amount eligible for cash discount in document currency", "DISC_AMT": "Cash discount amount in the currency of the currency types", "TAX_AMT": "Amount in Document Currency"}}]}