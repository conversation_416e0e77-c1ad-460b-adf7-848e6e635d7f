﻿

/*********************************************************************
/*                                                                   *
/*                           SAPNWRFC.INI                            *
/*                                                                   *
/*  By default the NW RFC library looks for the sapnwrfc.ini file    *
/*  in the current working directory of the process. If you want     *
/*  to change that behaviour, you can set an alternative directory   *
/*  using the API RfcSetIniPath(). With RfcReloadIniFile() the new   *
/*  file can then be (re-)loaded into memory.                        *
/*                                                                   *
/*********************************************************************


/*===================================================================*
/*                         DEFAULT Section                           *
/*                                                                   *
/* The default section defines some default or global properties     *
/* regarding connection management and tracing. Here is a sample     *
/* configuration.                                                    *
/*                                                                   *
/*===================================================================*


DEFAULT
# Trace all connections/destinations, which do not explicitly define
# their own trace level, with level 1. If this value is not set, the
# default is 0. Possible values are 0(off), 1(brief), 2(verbose), 3(full)
# RFC_TRACE=2

# The following four parameters are evaluated only once at process
# startup, not when reloading the sapnwrfc.ini file at a later point.

# By default, all trace files are written into the current working
# directory of the process. If you want to change that location, use
# RFC_TRACE_DIR=/absolute/path/to/tracedir

# By default, the NW RFC library creates one trace file per thread, in
# order to avoid multiple threads interleaving their trace output.
# If you want one trace file for the entire process, specify
# RFC_TRACE_TYPE=PROCESS

# When tracing data that contains non-ISO-8859-1 characters, the trace
# file becomes corrupted. In that case use the following parameter to
# change the file encoding of the trace file. Possible values are
# UTF-16 and UTF-8. If the parameter is not specified, the default platform
# encoding is used, which is CP1252 or ISO-8859-1 on most Windows systems,
# and UTF-8 on most Linux systems.
# On Windows UTF-16 shows better performance than UTF-8, because no additional
# conversion is required. On Linux, UTF-8 is recommended.
# RFC_TRACE_ENCODING=UTF-16

# In SM59, there is the option to "export" the trace level from ABAP side to
# an external RFC server program. If that flag is activated, the RFC library
# starts tracing incoming RFC calls with an RFC trace level of 2. In some cases
# it may be useful to refuse to accept the exported trace level from backend
# side, e.g when you want to prevent that an ABAP user can flood the discspace
# of the external server, or when payload data should not be written into
# trace files. In this case you can set the parameter
# RFC_ACCEPT_EXPORTED_TRACE=0
# and the RFC library will not write traces, even if the backend requests it.
# Possible values: 0 (for false) and 1 (for true).

# By default, the NW RFC library keeps two "rolling" trace files of maximum
# size 512 MB (in older versions: 64 MB). If during trouble-shooting this turns
# out to be not sufficient to capture the problematic situation in the trace,
# e.g. because it appears only at random after a non-deterministic runtime of
# the program, you can increase this file size by setting the parameter
# RFC_TRACE_MAX_FILE_SIZE=1024M
# As unit of measure you can use "M" for Megabytes or "G" for Gigabytes.

# Sometimes a developer of an RFC program uses the function RfcSetTraceLevel()
# with a trace level of 0 at the beginning of the program. In that case there is
# no way of ever activating RFC trace and trouble-shooting the program without
# re-compiling it. (Which may be particularly inconvenient, if it is a program
# obtained from a third party and source code is not available for it...)
# In that case you can set the parameter
# RFC_MINIMUM_TRACE_LEVEL=2
# and then the function RfcSetTraceLevel() can never set the trace level to a lower
# value than the one defined here.

# The global CPIC trace level used by the underlying CPIC libabry to write
# CPIC tracing information to the CPIC trace file cpic_<pid>.trc, where pid 
# is the process ID of the current process. The CPIC trace file
# is located in the same directory where the RFC trace files are located.
# A integer value between 0 to 3 can be set. The default is the value specified
# by the environment variable CPIC_TRACE if this environment variable is defined,
# otherwise the default is 0.  
# CPIC_TRACE=3

# Activating the CPIC keepalive per CPIC_KEEPALIVE (SAP note 2606606)
# Must be a value between 10 and 3600, where 0 turns the cpic keepalive off.
# CPIC_KEEPALIVE=60

# The global TCP/IP socket buffer size (in byte) used by the underlying CPIC/NI layer.
# The default value is 32768 bytes (32K). The optimal buffer size depends on several
# network environment factors including types of switches and systems, acknowledgment
# timing, error rates and network topology, memory size, and data transfer size. You 
# might need to try several values before you determine the ideal buffer size for your
# system. In most cases, the default value is adequate.
# NI_SOCKET_BUFFER_SIZE=32768 

# The Local_ADDRESS used by the underlying CPIC libabry to set the ADDRESS of Outgoing
# Network card
#
# e.g. LOCAL_ADDRESS=***********

# The global socket trace level used by the underlying NI libabry to write
# tracing information to the Websocket trace file ws_rfc_<pid>.trc, where pid 
# is the process ID of the current process. This trace file is located in the 
# same directory where the RFC trace files are located.
# An integer value between 0 to 3 can be set. The minium  trace level is the value 
# specified by the environment variable RFC_SOCKET_TRACE if this environment 
# variable is defined, otherwise the default is 0.
# RFC_SOCKET_TRACE=2

# Sets the absolute path and name of the sapcrypto library. The library can also
# be loaded via RfcLoadCryptoLibrary() programmatically. Once set it cannot be changed
# during runtime.
# TLS_SAPCRYPTOLIB=/absolute/path/to/file/cryptolib

/*===================================================================*
/*                       Destination Sections                        *
/*                                                                   *
/* Now there follow a couple of destination specific sections. Each  *
/* section defines the necessary logon parameters for either         *
/* a) openening a client connection to execute FMs in the SAP system *
/*    (pass DEST=... to RfcOpenConnection())                         *
/* b) starting a server to listen for incoming FM calls from the SAP *
/*    system                                                         *
/*    (pass DEST=... to RfcRegisterServer())                         *
/*                                                                   *
/* In both cases additional parameters can be passed in the same     *
/* RFC_CONNECTION_PARAMETER array, for example if you don't want to  *
/* keep the password in the ini file, or if you want to override the *
/* trace level dynamically.                                          *
/*                                                                   *
/* A new section is started by a line "DEST=..." and completed by    *
/* an empty line. Any line starting with "#" or "/*" or "//" is      *
/* regarded as a comment line.                                       *
/*                                                                   *
/*===================================================================*



# A client destination using load balancing and a Chinese username.
# Note that R3NAME (system ID) is not given, so in that case the DEST
# value (BIN) is used as system ID.
DEST=BIN
MSHOST=binmain
GROUP=PUBLIC
CLIENT=000
USER=老李
PASSWD=ides1234567
LANG=ZH
CODEPAGE=8400
TRACE=2

# A destination pointing to the same system as above, but using a 
# local application server for debugging 
DEST=LOCAL
ASHOST=WDFD00183930A
SYSNR=53
CLIENT=000
USER=rfctest
PASSWD=ides1234567
LANG=EN
NO_COMPRESSION=1
ABAP_DEBUG=1
TRACE=2

# A destination pointing to the same system as above, but using a
# different user for login. Note that in this case the DEST value
# needs to be a different one and therefore the R3NAME needs to be
# specified explicitly.
DEST=BIN2
R3NAME=BIN
GROUP=PUBLIC
MSHOST=binmain
CLIENT=000
USER=rfctest
PASSWD=ides1234567
LANG=EN
NO_COMPRESSION=1
#USE_SAPGUI=2
ABAP_DEBUG=1
#TRACE=2

# A destination using direct application server logon.
DEST=PRD
ASHOST=winnetou.bamberg.com
CLIENT=800
TRACE=0
USER=ALEREMOTE
LANG=EN
SYSNR=85

# A server destination for the above system.
DEST=PRD_Server
GWHOST=winnetou.bamberg.com
GWSERV=sapgw85
PROGRAM_ID=iltschi
SNC_MYNAME=p/secude:CN=ALEREMOTE, O=Karl-May-Verlag, C=DE
SNC_PARTNERNAME=p/secude:CN=PRD, O=Karl-May-Verlag, C=DE
SNC_LIB=C:\WINDOWS\system32\secude.dll
SNC_QOP=9


# A Websocket destination with TLS and Proxy. Column-based serialization in LAN mode is activated
DEST=WS_Client
WSHOST=winnetou2.bamberg.com
WSPORT=50018
CLIENT=000
USER=rfctest
PASSWD=ides1234567
PROXY_HOST=OldShatterhand.bamberg.com
PROXY_PORT=8081
PROXY_USER=BloodyFox
PROXY_PASSWD=ILoveWinnetou
TLS_CLIENT_PSE=C:\WINDOWS\system32\trustedServerCertificates.pse
#TLS_TRUST_ALL=ON
#USE_TLS=OFF
SERIALIZATION_FORMAT=CB_SERIALIZATION
COMPRESSION_TYPE=LAN


# A Websocket server destination. The server requires the client's certificate
DEST=WS_Server
WSPORT=50023
REG_COUNT=5
TLS_SERVER_PSE=C:\WINDOWS\system32\serverCertificateAndTrustedCAs.pse
TLS_SERVER_PARTNER_AUTH=REQUIRED


/*===================================================================*
/*                                                                   *
/* The following is a list of all supported parameters. The same     *
/* parameters can also be used in the RFC_CONNECTION_PARAMETER array *
/* when calling RfcOpenConnection() or RfcRegisterServer()           *
/*                                                                   *
/*===================================================================*


# 1. General Connection parameters
# These can be used with client and server programs

# SAPROUTER       : If the connection needs to be made through a firewall
			via a SAPRouter, specify the SAPRouter parameters in the
			following format:
			/H/hostname/S/portnumber
# SNC_MODE        : Specifies whether SNC should be activated for this connection or not. One of the following values:
			0 SNC off
			1 SNC on
# SNC_LIB         : Full path and name of third-party security library to
			use for SNC communication (authentivation, encryption and signatures)
			Alternatively you can set the environment variable SNC_LIB for 32bit programs and SNC_LIB_64 for 64bit programs.
# SNC_MYNAME      : Token/identifier representing the external RFC program
# SNC_PARTNERNAME : Token/identifier representing the backend system
# SNC_QOP         : One of the following values:
			1 Digital signature
			2 Digital signature and encryption
			3 Digital signature, encryption and user authentication
			8 Default value defined by back-end system
			9 Maximum value that the current security product supports
# TRACE           : One of 0(off), 1(brief), 2(verbose), 3(full)
# PCS             : "Partner character size". In 99.9% of the cases you
			don't need to bother with that. During the initial handshake
			the RFC library obtains the correct value from the backend and
			uses it from then on. One rare usecase is as follows: you know
			that the backend is Unicode and want to use a non-ISO-Latin-1
			username or password for the initial logon. As the initial handshake
			is done with ISO-Latin-1, the characters in username/passwd would
			break, resulting in a refused logon. In that case set PCS=2 and the
			RFC library will use Unicode for the initial handshake.
# CODEPAGE        : Similar to PCS above. You only need it if you want to connect
			to a non-Unicode backend using a non-ISO-Latin-1 username or password.
			The RFC library will then use that codepage for the initial handshake,
			thus preserving the characters in username/password.
			A few common values are:
			1401: ISO-Latin-2
			1500: ISO-Latin-5/Cyrillic
			1600: ISO-Latin-3
			1610: ISO-Latin-9/Turkish
			1700: ISO-Latin-7/Greek
			1800: ISO-Latin-8/Hebrew
			1900: ISO-Latin-4/Lithuanian/Latvian
			8000: Japanese
			8300: Traditional Chinese
			8400: Simplified Chinese
			8500: Korean
			8600: Thai
			8700: ISO-Latin-6/Arabic
			However, please note that these values can be customized in the backend.
			Better consult the backend sysadmin first, as otherwise things may go
			terribly wrong...
# NO_COMPRESSION  : By default the RFC protocol compresses tables, when they reach a
			size of 8KB or more. On very rare occasions you may want to turn this
			off, for example if you are transporting huge integer/binary tables with
			"random" data, where compression would have no effect except for burning CPU...
# ON_CCE          : "On Character Conversion Error". What shall the NW RFC lib do, when it encounters
			a character that does not exist in the target codepage, a broken character
			or a control character (0x00 - 0x19)? This parameter can
			take three values:
			0: Abort with an error message (default behaviour). Note that in this case
			   control characters (e.g. tabulator, carriage return or linefeed characters)
			   are not considered "illegal" and will therefore not cause an abort.
			1: Copy the character in a "round-trip compatible way". The resulting output
			   character may be "garbage" in the target codepage, but when converted back
			   to the source codepage, it will be the original character.
			2: Replace the character with a substitute symbol (usually a # character).
			   Note that in this case the control characters are replaced as well. If you need
			   the control characters, then you'll have to use option 0 or 1, depending on whether
			   you want the NW RFC Lib to abort the call in case of broken characters or not.
# CFIT            : "Conversion Fault Indicator Token". The substitute symbol used if ON_CCE=2.
			Needs to be given as hexadecimal value of a Unicode codepoint.
			The default is 0x0023 ("# character").



# 2. Parameters used in client programs

# USER      : Username
# ALIAS_USER: Username alias, can be used instead of USER. Mandatory for a websocket RFC connection to a cloud system
# PASSWD    : Password
# CLIENT    : The Client or "Mandant" to which to logon.
# LANG      : Logon Language. Either specify the two-character ISO-Code (like EN for English,
			KO for Korean) or the one-character SAP-specific code (like E for English,
			3 for Korean). Note that the ISO-codes are case-insensitiv, while the SAP
			codes are not! So 'D' logs you on in German, while 'd' logs you on in
			Serbian, if that language is installed in your system...

# PASSWORD_CHANGE_ENFORCED: Specify whether a user must change his password during logon when the backend system challenges 
				this user to do so because a) the password is initial; b) the password has expired; c) or a changed password
				policy enforces the change of the password. Possible values are:
				0: Do not enforce the change (default). If no PasswordChangeHandler is installed, the NW RFC Lib will use the
				   initial/expired password to login.
				1: Enforce the password change. In this case, the application has to call RfcInstallPasswordChangeHandler()
				   to install a callback function, which will be called by the NW RFC Library during a call to
				   RfcOpenConnection(). If no PasswordChangeHandler is installed or the callback function
				   returns a value other than RFC_OK, the call to RfcOpenConnection will fail with RFC_LOGON_FAILURE.
				Whether or not the NW RFC Library is able to change the password and login at all, also depends on the
				value of the backend profile parameter rfc/reject_expired_passwd. See SAP note 161146 for details.
				If the backend system rejects expired/initial passwords (the above profile parameter is set to "1"), then there
				is nothing you can do about this: you need to change the user's password via SAPGui.
# SNC_SSO   : Specify whether to use SNC single sign-on or not if SNC is enabled. Possible values are:
			0:  don't use SNC single sign-on
			1:  use SNC single sign-on (default)
# MYSAPSSO2 : Use this parameter instead of USER&PASSWD to log on with an SSO2 ticket (Single-Sign_On)
			or with an "Assertion" ticket (starting with backend release 7.00).
# GETSSO2   : Set this to 1, if the backend should generate an SSO2 ticket for your user.
			If RfcOpenConnection() succeeds, you can retrieve the ticket with
			RfcGetPartnerSSOTicket() and use it for further logons to systems supporting
			the same user base.
		!!! Note: No longer supported! SAP systems shall no longer issue tickets during user logon!!!
		!!! See also the documentation of the profile parameter login/create_sso2_ticket!!!
# X509CERT  : Use this parameter instead of USER&PASSWD, if you want to logon with an X.509
			certificate. The certificate needs to be Base64 encoded and needs to be
			mapped to a valid user in the backend's user configuration.
# EXTIDDATA : Old logon mechanism similar to SSO. No longer recommended.
# EXTIDTYPE : See EXTIDDATA.
# LCHECK    : If you set this to 0, RfcOpenConnection() only opens a network connection but
			does not perform the logon procedure. I.e. no user session will be created
			inside the backend system. Note that such a connection is not particularly
			useful: the only function module you can execute over it is RFC_PING...
# USE_SAPGUI: Specifies whether a SAPGui should be attached to the connection. Some (old) BAPIs
			need this, because they try to send screen output to the client while
			executing. Possible values are:
			0: no SAPGui (default)
			1: attach a visible SAPGui.
			2: attach a "hidden" SAPGui, which just receives and ignores the screen output.
			Note that for values other than 0: a SAPGui needs to be installed on the
			machine, where the client program is running. This can be either a normal
			Windows SAPGui or a Java Gui on Linux/Unix systems.
			Note on USE_SAPGUI=2: this type has a negative performance impact. If you
			are using only one function module that needs a Gui, and a large number of
			"normal" function modules, you should consider the following alternatives:
			a) Open the connection with USE_SAPGUI=1, and immediately after each RFC call
			   that may invoke SAPGui, make an RFC call to the FM SYSTEM_INVISIBLE_GUI on
			   the same connection to hide the SAPGui. Call your other FMs as usual.  
			b) Open two connections, one without USE_SAPGUI parameter and one with USE_SAPGUI=2.
			   Use the first connection for executing all "normal" FMs and the second connection
			   whenever you need to call the FM that uses the Gui.
# ABAP_DEBUG: Can be used for R/3 systems with release < 6.20, where "external breakpoints" are not
			yet available. The connection is opened in debug mode and the invoked function
			module can be steped through in the debugger. Possible values:
			0: no debugging (default)
			1: attach a visible SAPGui and break at the first ABAP statement of the
			invoked function module.
			Note that for debugging a SAPGui needs to be installed on the machine,
			where the client program is running. This can be either a normal Windows
			SAPGui or a Java Gui on Linux/Unix systems.
			For backend releases >= 6.20 use "external breakpoints" instead (see note
			668256), as this is more convenient and allows the debugger to run on any
			host, not only the host on which the RFC client program is running.

# NO_BASXML: If you set this parameter to a value other than 0 when opening a RFC connection, the basXml serialization
			format will be disabled for all function calls over this connection, even if the
			called function modules are enabled for the basXml serialization and the target system also supports
			the basXml serialization.    

# DELTA:     Specify whether to use delta-manager when serializing / deserializing table parameters passed by using TABLES clause.
			Possible values are:
			0:  don't use delta-manager
			1:  use delta-manager (default)
# SAPLOGON_ID: Identifies an entry in SAPLogon/saplogon.ini (as displayed by SAPLogon in the "Name" column). If you specify
			this parameter, the NW RFC library will obtain all necessary host, saprouter, SNC information etc. from the
			saplogon.ini file. (A list of the available IDs can be obtained with the function RfcGetSaplogonEntries().)
			The only further logon parameters required for opening a connection are then:

			a) If the SAPLogon entry is enabled for Secure Network Communication (SNC), then you only need to provide
			   the CLIENT as additional parameter. (Of course, further optional parameters like TRACE, LANG, ABAP_DEBUG
			   are possible.)
			b) If the SAPLogon entry is not enabled for SNC, or if the flag "SNC logon with user/password" is activated,
			   you need to provide CLIENT, USER and PASSWD as additional parameters.

			If using SNC and the environment variable SNC_LIB/SNC_LIB_64 is not set, you may need to add the parameter SNC_LIB.

# USE_REPOSITORY_ROUNDTRIP_OPTIMIZATION: The standard mechanism for reading function module descriptions from the backend's
			DDIC requires one DDID lookup for every structure/table that the function module in question possesses. As this may
			result in lots and lots of roundtrips to the backend, if the function module or BAPI has hundreds of structures and
			table parameters, a new mechanism was implemented in the DDIC, which allows to read the metadata of a function module
			in one go. You can even load several function modules (and their structures and tables) into the NW RFC library's
			DDIC cache at once, using the function RfcMetadataBatchQuery(), for example at initialization time of your application.
			See also the documentation of that function.
			If you want to use this improved DDIC lookup mechanism, first make sure that your backend system has at least the
			support package level described in SAP note 1456826, and then set this logon parameter to "1". All DDIC lookups over
			this client connection will then use the new mechanism.

# SERIALIZATION_FORMAT : Starting with release 7.51, the R/3 kernel supports a new serialization format,
			which should be faster especially for nested structures and tables and for tables under EXPORTING
			and IMPORTING. If your backend system has a sufficiently high release and patch level, you can
			activate the column-based serialization format to improve data transmission performance in certain
			scenarios. This parameter takes the following values:
			CB_SERIALIZATION: Use the column-based serialization format, if the backend supports it.
			CLASSIC_SERIALIZATION: Use the old format, even if the backend supports the new format. This is the
			     default value.
# COMPRESSION_TYPE     : In the column-based serialization, you can choose between two compression algorithms:
			LZ4 compression, which does not take much CPU time, but also does not compress very good, and
			zLib compression, which compresses very good, but also takes a lot of CPU time. In fast networks,
			the time it takes to transfer data is so small, that you don't get much improvement when compressing
			as good as possible. The CPU time lost during compression can never be made up during the data transfer
			over the network... Only in a slow network it may pay off to invest some time into a very good
			compression. Therefore this parameter takes the following values:
			LAN: Use fast LZ4 compression. Best option in fast networks. This is the default value.
			WAN: Use slow but better zLib compression. May be better in slow networks.
			OFF: Don't compress the data. Useful for trouble-shooting problems, so you can see the data in
			     plain text in the RFC trace.

# In addition you need parameters from one of the following two groups:


# 2.1 Parameters for direct application server logon

# ASHOST  : Hostname of the application server.
# SYSNR   : The backend's system number. (E.g. "01")


# 2.2 Parameters for load balancing

# MSHOST          : Hostname of the message server, which is doing the loadbalancing.
# MSSERV          : Not needed in most cases. Specify this parameter only, if the
			message server does not listen on the standard service "sapms<SysID>",
			or if this service is not defined in the services file and you need to
			specify the network port directly. Note: on Unix the services are
			defined in /etc/services, while on Windows they are defined in
			C:\WINDOWS\system32\drivers\etc\services.
# R3NAME or SYSID : The backend's system ID. (E.g. "H9C"). If this parameter is not
			specified, the value of DEST is used instead.
# GROUP           : The logon group from which the application server should be chosen.
			If this parameter is not specified, "PUBLIC" is used.
# USE_SYMBOLIC_NAMES : Defines, whether during group-logon the NW RFC library should use symbolic service names
			defined in /etc/services, like sapgw33, or hard-coded port numbers derived from the
			instance number, like 3300.
			Values:
			0: use port numbers (default)
			1: use service names



# 3. Parameters used in server programs

# SERVER_NAME          : An optional server name that distinguishes your server created via RfcCreateServer().
# TPNAME or PROGRAM_ID : The program ID used to identify the SM59 RFC destination, for which
				we want to receive requests.
# SYS_IDS              : A comma (,) separated list of IDs of those systems that are granted access.
				By default all systems are admitted.
# SNC_PARTNER_NAMES    : A vertical bar (|) separated list of SNC names of those systems that are granted access.
				By default all systems are admitted.
# REG_COUNT            : In servers created via RfcCreateServer(), you need to specify how many times the server
				should register at the gateway. This allows processing of parallel requests whithout any additional
				effort for the application programmer (like creating multiple threads and managing multiple registrations
				with their complicated ListenAndDispatch-Loop in those threads).
# MAX_REG_COUNT        : If a server processes stateful requests -- either because the ABAP side requests it via
				RFC_SET_REG_SERVER_PROPERTY, or because the C/C++ side requests it via RfcSetServerStateful(), it is possible
				that after a while all registered connections are blocked by long-running user sessions. New RFC requests
				from other users will then run into a timeout. If you want to prevent this, you can specify the extra
				parameter MAX_REG_COUNT. It instructs the server to replace every registration that gets reserved exclusively
				for one user session, with a fresh registration, as long as the total number of registrations has not yet
				exceeded MAX_REG_COUNT. (Of course, if more and more user sessions keep aquiring a stateful connection
				to themselves and never releasing them, this will not help... So you need to make sure that each stateful
				session will be released, when it is done. This can be done either by the ABAP side calling
				RFC_SET_REG_SERVER_PROPERTY again, this time with REG_EXCLUSIV = 'N', or by calling RfcSetServerStateful()
				with 0 (false).

In addition, one of the following two sets of parameters needs to be supplied, either for "direct registration"
or for "group registration".


# 3.1 Parameters for direct registration

In this case the RFC server registers directly at one given gateway (once, when using RfcRegisterServer(), or
REG_COUNT times, when using RfcCreateServer()).

# GWHOST    : Hostname of the RFC gateway at which to register. Usually some application server host.
# GWSERV    : Either specify "sapgw<SysNr>" or the port number directly, if that service is not defined
				in the services file. (Also see MSSERV for details on "services".)


# 3.2 Parameters for group registration

Group registration can only be used with RfcCreateServer()). In this case the server registers REG_COUNT times 
on each of the gateways/application servers given by the logon group.

Note that in this case you should leave the gateway parameters in the corresponding destination in SM59 empty,
so that every application server uses its own gateway. This feature can be used for two reasons:
a) Load balancing: every application server can send requests to the server program via its own gateway.
   (As opposed to all the application servers going via one single gateway, which may become a bottleneck.)
b) High Availablility: when using direct registration, if the application server, whose gateway is used for
   communication with this server program, is restarted or shut down, all remaining application servers also
   can no longer call the server program. However, with group registration, each application server uses its
   own gateway and is therefore not affected by a different gateway/app server being shut down.

# MSHOST          : Hostname of the message server, which is doing the loadbalancing.
# MSSERV          : Not needed in most cases. Specify this parameter only, if the message server does not listen
			on the standard service "sapms<SysID>", or if this service is not defined in the services file and you need to
			specify the network port directly. Note: on Unix the services are defined in /etc/services, while on Windows
			they are defined in C:\WINDOWS\system32\drivers\etc\services.
# R3NAME or SYSID : The backend's system ID. (E.g. "H9C").
# GROUP           : The logon group, against which to register. Note that the server registers at every application server
			in the group. This is different from client programs, where the group is used to pick one of the application servers
			and then to log on to this specific one.
			If this parameter is not specified, all app servers/gateways of the given system are used.
# LOGON_GROUP_CHECK_INTERVAL: A server with group registration checks periodically with the gateway, whether new
			application servers have become available in the given group (e.g. app servers, that had been shut down, are now
			running again, or servers have been added to or removed from the group). If new app servers are found, the
			RFC server program automatically registers at their gateways as well. The default value for this check period is
			300s, but can be overridden via this parameter, if you think that in your scenarion a longer period is sufficient
			(or you know that in the given group the number of app servers never changes...)
			The maximum supported value is INT_MAX = 2147483647.


# 4. Parameters for direct Websocket RFC communication

# 4.1 Parameters used in client programs

# WSHOST         : Hostname or IP address of the server to which a Websocket connection is to be opened
# WSPORT         : Port number or service string of the server's listening port for Websocket communication
# PROXY_HOST     : Optionally, hostname or IP address of the proxy to reroute the traffic 
# PROXY_PORT     : Optionally, port number or service string of the proxy's listening port to reroute the traffic
# PROXY_USER     : Optionally, proxy user name if authentication is needed for the configured proxy
# PROXY_PASSWD   : Optionally, proxy user password if authentication is needed for the configured proxy

#Optionally, if the connection is to be secured with SSL/TLS, you need to specify the following parameters:

# USE_TLS        : Activates SSL/TLS encryption. Set to 0 or 1. By default TLS is turned on (1).
# TLS_CLIENT_PSE : Specifies the PSE file containing the necessary certificates for TLS communication.
			A PSE file is a SAP proprietary certificate store, similar to a p12 file, containing the private key and
			the certificate chain to be used in the TLS handshake with the server, beginning with the server's public
			certificate and ending with the root CA certifcate. It should also contain the client certificate used for
			login at the server, if your client program does not use basic user & password authentication.
# TLS_CLIENT_CERTIFICATE_LOGON : Specifies, whether the program is to use a client certificate for logging in
			on the server side. In this case the PSE needs to contain exactly one certificate of type "client certificate"
			and its certificate chain. PSEs with multiple client certificates are not supported.
			Set to 0 or 1. By default this is turned off (0).
# TLS_TRUST_ALL  : Optionally, the program will not verify the server certificate and trust all TLS entities.
			Mostly used for troubleshooting. Set to 0 or 1. By default this is turned off (0).


# 4.2 Parameters used in server programs

# WSPORT    : Port number or service string of the server's listening port for Websocket communication
# REG_COUNT : Maximum number of requests that can be processed in parallel. In a registered server, this specifies
			the number of gateway registrations, in a direct server, it specifies the size of the threadpool used for
			processing the accepted connections.

#In the case the server shall be accessible via SSL/TLS, more parameters are necessary:

# USE_TLS                  : Activates SSL/TLS encryption. Set to 0 or 1. By default TLS is turned on (1).
# TLS_SERVER_PSE           : A file in PSE format, containing the private key and the certificate chain to be used,
			beginning with the server's public certificate and ending with the root CA certifcate. It should also contain the
			CA certificates to be used for trust validation of the presented client certificates.
# TLS_SERVER_PARTNER_AUTH  : Optionally, specifies whether the server requests a client certificate. The following values are applicable.        
			NONE     : The server does not ask for a client certificate. Client authentication is then done via basic user & password.
			REQUEST  : The server suggest to the peer to provide a client certificate, but continues the TLS handshake without client certificate.
							This means the client can choose between certificate based authentication and basic user & password based authentication.
			REQUIRED : The server requires the client to provide a client certificate. If the client does not present one in the TLS handshake,
							the server aborts the connection.
			The default value is REQUEST.

