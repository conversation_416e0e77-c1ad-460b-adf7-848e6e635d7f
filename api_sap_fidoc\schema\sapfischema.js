import joi from "joi";
import joidate from "@joi/date";

const Joi = joi.extend(joidate);

const joischema = {
    getFiDocuQuery: Joi.object().keys({
        page: Joi.number().required(),
        limit: Joi.number().required(),
        confirmCode: Joi.string().empty(null).empty(""),
        creationDate: Joi.string().empty(null).empty(""),
        statusCode: Joi.string().empty(null).empty(""),
        COMP_CODE: Joi.string().empty(null).empty(""),
        DOC_TYPE: Joi.string().empty(null).empty(""),
        DOC_DATE: Joi.string().empty(null).empty(""),
        PSTNG_DATE: Joi.string().empty(null).empty(""),
        HEADER_TXT: Joi.string().empty(null).empty(""),
        REF_DOC_NO: Joi.string().empty(null).empty(""),
        key: Joi.string().empty(null).empty(""),
    }),

    getfiDocuAlert: Joi.object().keys({
        _id: Joi.string().empty(null).empty(""),
        Comp: Joi.string().min(4).max(4).empty(null).empty(""),
        Email: Joi.string().email().empty(null).empty("")
    }),

    SavefiDocuAlert: Joi.object().keys({
        _id: Joi.string().empty(null).empty(""),
        Comp: Joi.string().min(4).max(4).required(),
        Email: Joi.string().email().required()
    }).required(),

    FiPostingPayload: Joi.object().keys({
        ReProcessText: Joi.string().max(100).empty(null).empty(""),
        Header: Joi.object({
            OBJ_TYPE: Joi.string().max(5).empty(null).empty(""),
            OBJ_KEY: Joi.string().max(20).empty(null).empty(""),
            OBJ_SYS: Joi.string().max(10).empty(null).empty(""),
            BUS_ACT: Joi.string().max(4).empty(null).empty(""),
            USERNAME: Joi.string().max(12).empty(null).empty(""),
            HEADER_TXT: Joi.string().max(25).empty(null).empty(""),
            COMP_CODE: Joi.string().max(4).empty(null).empty("").required(),
            DOC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
            PSTNG_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
            TRANS_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
            FISC_YEAR: Joi.string().max(4).empty(null).empty(""),
            FIS_PERIOD: Joi.string().max(2).empty(null).empty(""),
            DOC_TYPE: Joi.string().max(2).empty(null).empty(""),
            REF_DOC_NO: Joi.string().max(16).empty(null).empty(""),
            AC_DOC_NO: Joi.string().max(10).empty(null).empty(""),
            OBJ_KEY_R: Joi.string().max(20).empty(null).empty(""),
            REASON_REV: Joi.string().max(2).empty(null).empty(""),
            COMPO_ACC: Joi.string().max(4).empty(null).empty(""),
            REF_DOC_NO_LONG: Joi.string().max(35).empty(null).empty(""),
            ACC_PRINCIPLE: Joi.string().max(4).empty(null).empty(""),
            NEG_POSTNG: Joi.string().max(1).empty(null).empty(""),
            OBJ_KEY_INV: Joi.string().max(20).empty(null).empty(""),
            BILL_CATEGORY: Joi.string().max(1).empty(null).empty(""),
            VATDATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
            INVOICE_REC_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
            ECS_ENV: Joi.string().max(10).empty(null).empty(""),
            PARTIAL_REV: Joi.string().max(1).empty(null).empty(""),
            DOC_STATUS: Joi.string().max(1).empty(null).empty("")
        }).required(),
        Items: Joi.array().items({
            Item_Number: Joi.string().min(1).max(10).required(),
            General_Ledger: Joi.object({
                GL_ACCOUNT: Joi.string().max(10).empty(null).empty(""),
                ITEM_TEXT: Joi.string().empty(null).empty(""),
                STAT_CON: Joi.string().max(1).empty(null).empty(""),
                LOG_PROC: Joi.string().max(6).empty(null).empty(""),
                AC_DOC_NO: Joi.string().max(10).empty(null).empty(""),
                REF_KEY_1: Joi.string().max(12).empty(null).empty(""),
                REF_KEY_2: Joi.string().max(12).empty(null).empty(""),
                REF_KEY_3: Joi.string().max(20).empty(null).empty(""),
                ACCT_KEY: Joi.string().max(3).empty(null).empty(""),
                ACCT_TYPE: Joi.string().max(1).empty(null).empty(""),
                DOC_TYPE: Joi.string().max(2).empty(null).empty(""),
                COMP_CODE: Joi.string().max(4).empty(null).empty(""),
                BUS_AREA: Joi.string().max(4).empty(null).empty(""),
                FUNC_AREA: Joi.string().max(4).empty(null).empty(""),
                PLANT: Joi.string().max(4).empty(null).empty(""),
                FIS_PERIOD: Joi.string().max(2).empty(null).empty(""),
                FISC_YEAR: Joi.string().max(4).empty(null).empty(""),
                PSTNG_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                VALUE_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                FM_AREA: Joi.string().max(4).empty(null).empty(""),
                CUSTOMER: Joi.string().max(10).empty(null).empty(""),
                CSHDIS_IND: Joi.string().max(1).empty(null).empty(""),
                VENDOR_NO: Joi.string().max(10).empty(null).empty(""),
                ALLOC_NMBR: Joi.string().max(18).empty(null).empty(""),
                TAX_CODE: Joi.string().max(2).empty(null).empty(""),
                TAXJURCODE: Joi.string().max(15).empty(null).empty(""),
                EXT_OBJECT_ID: Joi.string().max(34).empty(null).empty(""),
                BUS_SCENARIO: Joi.string().max(16).empty(null).empty(""),
                COSTOBJECT: Joi.string().max(12).empty(null).empty(""),
                COSTCENTER: Joi.string().max(10).empty(null).empty(""),
                ACTTYPE: Joi.string().max(6).empty(null).empty(""),
                PROFIT_CTR: Joi.string().max(10).empty(null).empty(""),
                PART_PRCTR: Joi.string().max(10).empty(null).empty(""),
                NETWORK: Joi.string().max(12).empty(null).empty(""),
                WBS_ELEMENT: Joi.string().max(24).empty(null).empty(""),
                ORDERID: Joi.string().max(12).empty(null).empty(""),
                ORDER_ITNO: Joi.string().max(4).empty(null).empty(""),
                ROUTING_NO: Joi.string().max(10).empty(null).empty(""),
                ACTIVITY: Joi.string().max(4).empty(null).empty(""),
                COND_TYPE: Joi.string().max(4).empty(null).empty(""),
                COND_COUNT: Joi.string().max(2).empty(null).empty(""),
                COND_ST_NO: Joi.string().max(3).empty(null).empty(""),
                FUND: Joi.string().max(10).empty(null).empty(""),
                FUNDS_CTR: Joi.string().max(16).empty(null).empty(""),
                CMMT_ITEM: Joi.string().max(14).empty(null).empty(""),
                CO_BUSPROC: Joi.string().max(12).empty(null).empty(""),
                ASSET_NO: Joi.string().max(12).empty(null).empty(""),
                SUB_NUMBER: Joi.string().max(4).empty(null).empty(""),
                BILL_TYPE: Joi.string().max(4).empty(null).empty(""),
                SALES_ORD: Joi.string().max(10).empty(null).empty(""),
                S_ORD_ITEM: Joi.string().max(6).empty(null).empty(""),
                DISTR_CHAN: Joi.string().max(2).empty(null).empty(""),
                DIVISION: Joi.string().max(2).empty(null).empty(""),
                SALESORG: Joi.string().max(4).empty(null).empty(""),
                SALES_GRP: Joi.string().max(3).empty(null).empty(""),
                SALES_OFF: Joi.string().max(4).empty(null).empty(""),
                SOLD_TO: Joi.string().max(10).empty(null).empty(""),
                DE_CRE_IND: Joi.string().max(1).empty(null).empty(""),
                P_EL_PRCTR: Joi.string().max(10).empty(null).empty(""),
                XMFRW: Joi.string().max(1).empty(null).empty(""),
                QUANTITY: Joi.string().max(13).empty(null).empty(""),
                BASE_UOM: Joi.string().max(3).empty(null).empty(""),
                BASE_UOM_ISO: Joi.string().max(3).empty(null).empty(""),
                INV_QTY: Joi.string().max(13).empty(null).empty(""),
                INV_QTY_SU: Joi.string().max(13).empty(null).empty(""),
                SALES_UNIT: Joi.string().max(3).empty(null).empty(""),
                SALES_UNIT_ISO: Joi.string().max(3).empty(null).empty(""),
                PO_PR_QNT: Joi.string().max(13).empty(null).empty(""),
                PO_PR_UOM: Joi.string().max(3).empty(null).empty(""),
                PO_PR_UOM_ISO: Joi.string().max(3).empty(null).empty(""),
                ENTRY_QNT: Joi.string().max(13).empty(null).empty(""),
                ENTRY_UOM: Joi.string().max(3).empty(null).empty(""),
                ENTRY_UOM_ISO: Joi.string().max(3).empty(null).empty(""),
                VOLUME: Joi.string().max(15).empty(null).empty(""),
                VOLUMEUNIT: Joi.string().max(3).empty(null).empty(""),
                VOLUMEUNIT_ISO: Joi.string().max(3).empty(null).empty(""),
                GROSS_WT: Joi.string().max(15).empty(null).empty(""),
                NET_WEIGHT: Joi.string().max(15).empty(null).empty(""),
                UNIT_OF_WT: Joi.string().max(3).empty(null).empty(""),
                UNIT_OF_WT_ISO: Joi.string().max(3).empty(null).empty(""),
                ITEM_CAT: Joi.string().max(1).empty(null).empty(""),
                MATERIAL: Joi.string().max(18).empty(null).empty(""),
                MATL_TYPE: Joi.string().max(4).empty(null).empty(""),
                MVT_IND: Joi.string().max(1).empty(null).empty(""),
                REVAL_IND: Joi.string().max(1).empty(null).empty(""),
                ORIG_GROUP: Joi.string().max(4).empty(null).empty(""),
                ORIG_MAT: Joi.string().max(1).empty(null).empty(""),
                SERIAL_NO: Joi.string().max(2).empty(null).empty(""),
                PART_ACCT: Joi.string().max(10).empty(null).empty(""),
                TR_PART_BA: Joi.string().max(4).empty(null).empty(""),
                TRADE_ID: Joi.string().max(6).empty(null).empty(""),
                VAL_AREA: Joi.string().max(4).empty(null).empty(""),
                VAL_TYPE: Joi.string().max(10).empty(null).empty(""),
                ASVAL_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                PO_NUMBER: Joi.string().max(10).empty(null).empty(""),
                PO_ITEM: Joi.string().max(5).empty(null).empty(""),
                ITM_NUMBER: Joi.string().max(6).empty(null).empty(""),
                COND_CATEGORY: Joi.string().max(1).empty(null).empty(""),
                FUNC_AREA_LONG: Joi.string().max(16).empty(null).empty(""),
                CMMT_ITEM_LONG: Joi.string().max(24).empty(null).empty(""),
                GRANT_NBR: Joi.string().max(20).empty(null).empty(""),
                CS_TRANS_T: Joi.string().max(3).empty(null).empty(""),
                MEASURE: Joi.string().max(24).empty(null).empty(""),
                SEGMENT: Joi.string().max(10).empty(null).empty(""),
                PARTNER_SEGMENT: Joi.string().max(10).empty(null).empty(""),
                RES_DOC: Joi.string().max(10).empty(null).empty(""),
                RES_ITEM: Joi.string().max(3).empty(null).empty(""),
                BILLING_PERIOD_START_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                BILLING_PERIOD_END_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                PPA_EX_IND: Joi.string().max(1).empty(null).empty(""),
                FASTPAY: Joi.string().max(1).empty(null).empty(""),
                PARTNER_GRANT_NBR: Joi.string().max(20).empty(null).empty(""),
                BUDGET_PERIOD: Joi.string().max(10).empty(null).empty(""),
                PARTNER_BUDGET_PERIOD: Joi.string().max(10).empty(null).empty(""),
                PARTNER_FUND: Joi.string().max(10).empty(null).empty(""),
                ITEMNO_TAX: Joi.string().max(6).empty(null).empty("")
            }),
            Customer: Joi.object({
                CUSTOMER: Joi.string().max(10).empty(null).empty(""),
                GL_ACCOUNT: Joi.string().max(10).empty(null).empty(""),
                REF_KEY_1: Joi.string().max(12).empty(null).empty(""),
                REF_KEY_2: Joi.string().max(12).empty(null).empty(""),
                REF_KEY_3: Joi.string().max(20).empty(null).empty(""),
                COMP_CODE: Joi.string().max(4).empty(null).empty(""),
                BUS_AREA: Joi.string().max(4).empty(null).empty(""),
                PMNTTRMS: Joi.string().max(4).empty(null).empty(""),
                BLINE_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                DSCT_DAYS1: Joi.string().max(3).empty(null).empty(""),
                DSCT_DAYS2: Joi.string().max(3).empty(null).empty(""),
                NETTERMS: Joi.string().max(3).empty(null).empty(""),
                DSCT_PCT1: Joi.string().max(5).empty(null).empty(""),
                DSCT_PCT2: Joi.string().max(5).empty(null).empty(""),
                PYMT_METH: Joi.string().max(1).empty(null).empty(""),
                PMTMTHSUPL: Joi.string().max(2).empty(null).empty(""),
                PAYMT_REF: Joi.string().max(30).empty(null).empty(""),
                DUNN_KEY: Joi.string().max(1).empty(null).empty(""),
                DUNN_BLOCK: Joi.string().max(1).empty(null).empty(""),
                PMNT_BLOCK: Joi.string().max(1).empty(null).empty(""),
                VAT_REG_NO: Joi.string().max(20).empty(null).empty(""),
                ALLOC_NMBR: Joi.string().max(18).empty(null).empty(""),
                ITEM_TEXT: Joi.string().empty(null).empty(""),
                PARTNER_BK: Joi.string().max(4).empty(null).empty(""),
                SCBANK_IND: Joi.string().max(3).empty(null).empty(""),
                BUSINESSPLACE: Joi.string().max(4).empty(null).empty(""),
                SECTIONCODE: Joi.string().max(4).empty(null).empty(""),
                BRANCH: Joi.string().max(10).empty(null).empty(""),
                PYMT_CUR: Joi.string().max(5).empty(null).empty(""),
                PYMT_CUR_ISO: Joi.string().max(3).empty(null).empty(""),
                PYMT_AMT: Joi.string().max(23).empty(null).empty(""),
                C_CTR_AREA: Joi.string().max(4).empty(null).empty(""),
                BANK_ID: Joi.string().max(5).empty(null).empty(""),
                SUPCOUNTRY: Joi.string().max(3).empty(null).empty(""),
                SUPCOUNTRY_ISO: Joi.string().max(2).empty(null).empty(""),
                TAX_CODE: Joi.string().max(2).empty(null).empty(""),
                TAXJURCODE: Joi.string().max(15).empty(null).empty(""),
                TAX_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                SP_GL_IND: Joi.string().max(1).empty(null).empty(""),
                PARTNER_GUID: Joi.string().max(32).empty(null).empty(""),
                ALT_PAYEE: Joi.string().max(10).empty(null).empty(""),
                ALT_PAYEE_BANK: Joi.string().max(4).empty(null).empty(""),
                DUNN_AREA: Joi.string().max(2).empty(null).empty(""),
                CASE_GUID: Joi.string().max(32).empty(null).empty(""),
                PROFIT_CTR: Joi.string().max(10).empty(null).empty(""),
                FUND: Joi.string().max(10).empty(null).empty(""),
                GRANT_NBR: Joi.string().max(20).empty(null).empty(""),
                MEASURE: Joi.string().max(24).empty(null).empty(""),
                HOUSEBANKACCTID: Joi.string().max(5).empty(null).empty(""),
                RES_DOC: Joi.string().max(10).empty(null).empty(""),
                RES_ITEM: Joi.string().max(3).empty(null).empty(""),
                FUND_LONG: Joi.string().max(20).empty(null).empty(""),
                DISPUTE_IF_TYPE: Joi.string().max(1).empty(null).empty(""),
                BUDGET_PERIOD: Joi.string().max(10).empty(null).empty(""),
                PAYS_PROV: Joi.string().max(4).empty(null).empty(""),
                PAYS_TRAN: Joi.string().max(35).empty(null).empty(""),
                SEPA_MANDATE_ID: Joi.string().max(35).empty(null).empty(""),
                PART_BUSINESSPLACE: Joi.string().max(5).empty(null).empty(""),
                REP_COUNTRY_EU: Joi.string().max(3).empty(null).empty("")
            }),
            CustomerClearing: Joi.array().items({
                BELNR: Joi.string().max(10).empty(null).empty(""),
                BUKRS: Joi.string().max(4).empty(null).empty(""),
                GJAHR: Joi.string().max(4).empty(null).empty(""),
                BUZEI: Joi.string().max(3).empty(null).empty("")
            }),
            Vendor: Joi.object({
                VENDOR_NO: Joi.string().max(10).empty(null).empty(""),
                GL_ACCOUNT: Joi.string().max(10).empty(null).empty(""),
                REF_KEY_1: Joi.string().max(12).empty(null).empty(""),
                REF_KEY_2: Joi.string().max(12).empty(null).empty(""),
                REF_KEY_3: Joi.string().max(20).empty(null).empty(""),
                COMP_CODE: Joi.string().max(4).empty(null).empty(""),
                BUS_AREA: Joi.string().max(4).empty(null).empty(""),
                PMNTTRMS: Joi.string().max(4).empty(null).empty(""),
                BLINE_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                DSCT_DAYS1: Joi.string().max(3).empty(null).empty(""),
                DSCT_DAYS2: Joi.string().max(3).empty(null).empty(""),
                NETTERMS: Joi.string().max(3).empty(null).empty(""),
                DSCT_PCT1: Joi.string().max(5).empty(null).empty(""),
                DSCT_PCT2: Joi.string().max(5).empty(null).empty(""),
                PYMT_METH: Joi.string().max(1).empty(null).empty(""),
                PMTMTHSUPL: Joi.string().max(2).empty(null).empty(""),
                PMNT_BLOCK: Joi.string().max(1).empty(null).empty(""),
                SCBANK_IND: Joi.string().max(3).empty(null).empty(""),
                SUPCOUNTRY: Joi.string().max(3).empty(null).empty(""),
                SUPCOUNTRY_ISO: Joi.string().max(2).empty(null).empty(""),
                BLLSRV_IND: Joi.string().max(1).empty(null).empty(""),
                ALLOC_NMBR: Joi.string().max(18).empty(null).empty(""),
                ITEM_TEXT: Joi.string().empty(null).empty(""),
                PO_SUB_NO: Joi.string().max(11).empty(null).empty(""),
                PO_CHECKDG: Joi.string().max(2).empty(null).empty(""),
                PO_REF_NO: Joi.string().max(27).empty(null).empty(""),
                W_TAX_CODE: Joi.string().max(2).empty(null).empty(""),
                BUSINESSPLACE: Joi.string().max(4).empty(null).empty(""),
                SECTIONCODE: Joi.string().max(4).empty(null).empty(""),
                INSTR1: Joi.string().max(2).empty(null).empty(""),
                INSTR2: Joi.string().max(2).empty(null).empty(""),
                INSTR3: Joi.string().max(2).empty(null).empty(""),
                INSTR4: Joi.string().max(2).empty(null).empty(""),
                BRANCH: Joi.string().max(10).empty(null).empty(""),
                PYMT_CUR: Joi.string().max(5).empty(null).empty(""),
                PYMT_AMT: Joi.string().max(23).empty(null).empty(""),
                PYMT_CUR_ISO: Joi.string().max(3).empty(null).empty(""),
                SP_GL_IND: Joi.string().max(1).empty(null).empty(""),
                TAX_CODE: Joi.string().max(2).empty(null).empty(""),
                TAX_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                TAXJURCODE: Joi.string().max(15).empty(null).empty(""),
                ALT_PAYEE: Joi.string().max(10).empty(null).empty(""),
                ALT_PAYEE_BANK: Joi.string().max(4).empty(null).empty(""),
                PARTNER_BK: Joi.string().max(4).empty(null).empty(""),
                BANK_ID: Joi.string().max(5).empty(null).empty(""),
                PARTNER_GUID: Joi.string().max(32).empty(null).empty(""),
                PROFIT_CTR: Joi.string().max(10).empty(null).empty(""),
                FUND: Joi.string().max(10).empty(null).empty(""),
                GRANT_NBR: Joi.string().max(20).empty(null).empty(""),
                MEASURE: Joi.string().max(24).empty(null).empty(""),
                HOUSEBANKACCTID: Joi.string().max(5).empty(null).empty(""),
                BUDGET_PERIOD: Joi.string().max(10).empty(null).empty(""),
                PPA_EX_IND: Joi.string().max(1).empty(null).empty(""),
                PART_BUSINESSPLACE: Joi.string().max(5).empty(null).empty(""),
                PAYMT_REF: Joi.string().max(30).empty(null).empty("")
            }),
            Tax: Joi.object({
                GL_ACCOUNT: Joi.string().max(10).empty(null).empty(""),
                COND_KEY: Joi.string().max(4).empty(null).empty(""),
                ACCT_KEY: Joi.string().max(3).empty(null).empty(""),
                TAX_CODE: Joi.string().max(2).empty(null).empty(""),
                TAX_RATE: Joi.string().max(7).empty(null).empty(""),
                TAX_DATE: Joi.date().format("YYYYMMDD").raw().empty(null).empty(""),
                TAXJURCODE: Joi.string().max(15).empty(null).empty(""),
                TAXJURCODE_DEEP: Joi.string().max(15).empty(null).empty(""),
                TAXJURCODE_LEVEL: Joi.string().max(1).empty(null).empty(""),
                ITEMNO_TAX: Joi.string().max(6).empty(null).empty(""),
                DIRECT_TAX: Joi.string().max(1).empty(null).empty("")
            }),
            Currency: Joi.object({
                CURR_TYPE: Joi.string().max(2).empty(null).empty(""),
                CURRENCY: Joi.string().max(5).empty(null).empty(""),
                CURRENCY_ISO: Joi.string().max(3).empty(null).empty(""),
                AMT_DOCCUR: Joi.string().max(23).empty(null).empty(""),
                EXCH_RATE: Joi.string().max(9).empty(null).empty(""),
                EXCH_RATE_V: Joi.string().max(9).empty(null).empty(""),
                AMT_BASE: Joi.string().max(23).empty(null).empty(""),
                DISC_BASE: Joi.string().max(23).empty(null).empty(""),
                DISC_AMT: Joi.string().max(23).empty(null).empty(""),
                TAX_AMT: Joi.string().max(23).empty(null).empty("")
            }),
        }).required(),
    }).required()

};

export default joischema;